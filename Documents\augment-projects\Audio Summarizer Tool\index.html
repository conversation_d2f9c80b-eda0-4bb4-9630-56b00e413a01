<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audio Summarizer Tool</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">
                    <i class="fas fa-microphone-alt"></i>
                    Audio Summarizer
                </h1>
                <div class="header-controls">
                    <button id="settingsBtn" class="icon-btn" title="Settings">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- API Configuration Panel -->
            <div id="apiConfigPanel" class="config-panel">
                <div class="config-content">
                    <h3>API Configuration</h3>
                    <div class="input-group">
                        <label for="geminiApiKey">Google Gemini API Key</label>
                        <input type="password" id="geminiApiKey" placeholder="Enter your Gemini API key">
                        <button id="saveApiKey" class="btn btn-primary">Save</button>
                    </div>
                    <p class="config-note">
                        <i class="fas fa-info-circle"></i>
                        Your API key is stored locally and never sent to our servers.
                    </p>
                </div>
            </div>

            <!-- Audio Controls -->
            <div class="audio-controls">
                <div class="control-group">
                    <h3>Audio Input</h3>
                    <div class="input-options">
                        <label class="checkbox-label">
                            <input type="checkbox" id="microphoneInput" checked>
                            <span class="checkmark"></span>
                            Microphone
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="systemAudioInput">
                            <span class="checkmark"></span>
                            System Audio
                        </label>
                    </div>
                </div>

                <div class="recording-controls">
                    <button id="startRecording" class="btn btn-record">
                        <i class="fas fa-play"></i>
                        Start Recording
                    </button>
                    <button id="stopRecording" class="btn btn-stop" disabled>
                        <i class="fas fa-stop"></i>
                        Stop Recording
                    </button>
                    <button id="pauseRecording" class="btn btn-pause" disabled>
                        <i class="fas fa-pause"></i>
                        Pause
                    </button>
                </div>

                <div class="audio-visualizer">
                    <canvas id="audioCanvas" width="800" height="100"></canvas>
                </div>

                <div class="recording-status">
                    <div class="status-indicator">
                        <span id="recordingStatus" class="status-text">Ready to record</span>
                        <span id="recordingTime" class="recording-time">00:00</span>
                    </div>
                    <div class="audio-levels">
                        <div class="level-meter">
                            <div id="micLevel" class="level-bar"></div>
                            <label>Mic</label>
                        </div>
                        <div class="level-meter">
                            <div id="systemLevel" class="level-bar"></div>
                            <label>System</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Processing Panel -->
            <div class="processing-panel">
                <div class="processing-options">
                    <h3>Processing Options</h3>
                    <div class="option-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="realTimeProcessing">
                            <span class="checkmark"></span>
                            Real-time Processing
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="speakerIdentification" checked>
                            <span class="checkmark"></span>
                            Speaker Identification
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="generatePodcast">
                            <span class="checkmark"></span>
                            Generate Podcast
                        </label>
                    </div>
                </div>

                <button id="processAudio" class="btn btn-primary" disabled>
                    <i class="fas fa-brain"></i>
                    Process with AI
                </button>
            </div>

            <!-- Results Panel -->
            <div class="results-panel">
                <div class="results-header">
                    <h3>Results</h3>
                    <div class="results-controls">
                        <button id="exportResults" class="btn btn-secondary" disabled>
                            <i class="fas fa-download"></i>
                            Export
                        </button>
                        <button id="clearResults" class="btn btn-secondary">
                            <i class="fas fa-trash"></i>
                            Clear
                        </button>
                    </div>
                </div>

                <div class="results-content">
                    <div class="tabs">
                        <button class="tab-btn active" data-tab="transcription">Transcription</button>
                        <button class="tab-btn" data-tab="summary">Summary</button>
                        <button class="tab-btn" data-tab="speakers">Speakers</button>
                        <button class="tab-btn" data-tab="podcast">Podcast</button>
                    </div>

                    <div class="tab-content">
                        <div id="transcriptionTab" class="tab-panel active">
                            <div id="transcriptionContent" class="content-area">
                                <p class="placeholder-text">Transcription will appear here after processing...</p>
                            </div>
                        </div>

                        <div id="summaryTab" class="tab-panel">
                            <div id="summaryContent" class="content-area">
                                <p class="placeholder-text">Summary will appear here after processing...</p>
                            </div>
                        </div>

                        <div id="speakersTab" class="tab-panel">
                            <div id="speakersContent" class="content-area">
                                <p class="placeholder-text">Speaker analysis will appear here after processing...</p>
                            </div>
                        </div>

                        <div id="podcastTab" class="tab-panel">
                            <div id="podcastContent" class="content-area">
                                <div class="podcast-controls">
                                    <audio id="podcastPlayer" controls style="display: none;">
                                        <source id="podcastSource" type="audio/mpeg">
                                        Your browser does not support the audio element.
                                    </audio>
                                    <button id="generatePodcastBtn" class="btn btn-primary">
                                        <i class="fas fa-podcast"></i>
                                        Generate Podcast
                                    </button>
                                </div>
                                <p class="placeholder-text">Generated podcast will appear here...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="loading-overlay">
            <div class="loading-content">
                <div class="spinner"></div>
                <p id="loadingText">Processing audio...</p>
                <div class="progress-bar">
                    <div id="progressFill" class="progress-fill"></div>
                </div>
            </div>
        </div>

        <!-- Toast Notifications -->
        <div id="toastContainer" class="toast-container"></div>
    </div>

    <script src="audio-processor.js"></script>
    <script src="gemini-api.js"></script>
    <script src="speaker-detection.js"></script>
    <script src="podcast-generator.js"></script>
    <script src="app.js"></script>
</body>
</html>
